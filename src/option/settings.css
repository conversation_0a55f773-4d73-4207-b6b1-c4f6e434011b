/* 
 * settings.css
 * 设置页面的样式文件，采用MVC架构中View层所需的样式
 */

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  color: #333;
  display: flex;
  min-height: 100vh;
}

/* 左侧导航栏 */
.sidebar {
  width: 220px;
  background-color: #f5f7f9;
  border-right: 1px solid #e0e0e0;
  padding: 20px 0;
}

.nav-item {
  padding: 12px 24px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background-color: #e8eef5;
}

.nav-item.active {
  background-color: #e8eef5;
  border-left-color: #4285f4;
  font-weight: 500;
}

/* 右侧内容区 */
.content {
  flex: 1;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.section {
  display: none;
}

.section.active {
  display: block;
}

header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 10px;
}

h1 {
  color: #4285f4;
  font-size: 24px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.description {
  color: #757575;
  font-size: 14px;
}

/* 通用表单样式 */
.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  font-weight: 500;
  margin-bottom: 6px;
}

input[type="text"],
input[type="password"],
select,
textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

input[type="number"] {
  width: 120px;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

input[type="range"] {
  flex: 1;
}

.slider-value {
  width: 40px;
  text-align: center;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4285f4;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* 模型卡片样式 */
.model-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.model-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  transition: box-shadow 0.3s, border-color 0.3s;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.model-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.model-card.active {
  border-color: #4285f4;
}

/* 模型卡片头部 */
.model-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
}

.model-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.custom-badge {
  background-color: #4285f4;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 8px;
  font-weight: normal;
}

/* 模型卡片主体 */
.model-body {
  padding: 16px;
  flex: 1;
}

.model-body .form-group {
  margin-bottom: 12px;
}

.model-body .form-group:last-child {
  margin-bottom: 0;
}

.model-body label {
  font-size: 13px;
  font-weight: 500;
  color: #555;
  margin-bottom: 4px;
}

.model-body input[type="text"],
.model-body input[type="password"] {
  font-size: 13px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* 模型卡片底部 */
.model-footer {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

/* 开关容器样式 */
.model-header .switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.model-header .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.model-header .switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .3s;
  border-radius: 24px;
}

.model-header .switch .slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
}

.model-header .switch input:checked + .slider {
  background-color: #4285f4;
}

.model-header .switch input:checked + .slider:before {
  transform: translateX(20px);
}

/* 删除按钮样式 */
.delete-btn {
  background-color: #fff;
  color: #d93025;
  border: 1px solid #d93025;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s;
}

.delete-btn:hover {
  background-color: #d93025;
  color: white;
}

/* 小型开关样式 */
.switch.small {
  width: 40px;
  height: 20px;
}

.switch.small .slider:before {
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
}

.switch.small input:checked + .slider:before {
  transform: translateX(20px);
}

/* 模型设置弹窗 */
.model-settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.model-settings-modal.active {
  opacity: 1;
  visibility: visible;
}

/* 添加弹窗统一容器样式 */
.model-settings-container {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
}

.model-settings-modal .modal-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-settings-modal .modal-content {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.model-settings-modal .modal-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

.model-settings-modal h3 {
  margin: 0;
  font-size: 18px;
}

/* 添加一些辅助样式 */
.model-field {
  margin-bottom: 12px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #757575;
}

.save-btn {
  min-width: 100px;
}

/* 按钮样式 */
.btn {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn:hover {
  background-color: #2b76e5;
}

.btn-secondary {
  background-color: #f2f2f2;
  color: #333;
}

.btn-secondary:hover {
  background-color: #e6e6e6;
}

.btn-add-model {
  margin-bottom: 20px;
}

/* 为添加按钮添加加号图标样式 */
.btn span::before {
  content: '';
  display: inline-block;
  margin-right: 4px;
}

/* 底部操作栏 */
.action-bar {
  position: sticky;
  bottom: 0;
  background-color: white;
  padding: 16px 0;
  border-top: 1px solid #e0e0e0;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  display: none;
  justify-content: center;
  align-items: center;
}

.modal.active {
  opacity: 1;
  visibility: visible;
  display: flex;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 24px rgba(0,0,0,0.2);
}

.modal-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-weight: 500;
  font-size: 18px;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #757575;
}

.modal-body {
  padding: 16px;
}

.modal-footer {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 复选框容器 */
.checkbox-container {
  margin-top: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.checkbox-item input[type="checkbox"] {
  margin-right: 8px;
}

/* 标记必填项 */
.required::after {
  content: " *";
  color: #d93025;
}

/* 状态信息 */
.status {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  display: none;
}

.status.success {
  display: block;
  background-color: #d9f7dc;
  color: #1e8e3e;
}

.status.error {
  display: block;
  background-color: #fce8e6;
  color: #d93025;
}

/* 测试连接按钮样式 */
.test-connection-btn {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.test-connection-btn:hover {
  background-color: #2b76e5;
}

.test-connection-btn:disabled {
  background-color: #a9c5f7;
  cursor: not-allowed;
}

/* 测试结果样式 */
.test-result {
  margin-top: 8px;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
}

.test-result.success {
  background-color: #d9f7dc;
  color: #1e8e3e;
  border: 1px solid #b7e9bd;
}

.test-result.error {
  background-color: #fce8e6;
  color: #d93025;
  border: 1px solid #f6c7c2;
}

/* 加载指示器样式 */
.test-connection-field {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.test-btn {
  background-color: #4285f4;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background-color: #2b76e5;
}

/* 测试状态样式 */
.test-status {
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  margin-left: 8px;
}

.test-status.testing {
  background-color: #e8f0fe;
  color: #1a73e8;
  display: flex;
  align-items: center;
  gap: 8px;
}

.test-status.success {
  background-color: #d9f7dc;
  color: #1e8e3e;
}

.test-status.error {
  background-color: #fce8e6;
  color: #d93025;
}

.test-response {
  margin-top: 4px;
  font-size: 12px;
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 3px;
  color: #5f6368;
  font-family: monospace;
  word-break: break-all;
}

.loader {
  display: flex;
  align-items: center;
  gap: 4px;
}

.loader-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #4285f4;
  animation: bounce 1.2s infinite ease-in-out;
}

.loader-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loader-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% { 
    transform: scale(0);
  }
  40% { 
    transform: scale(1.0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
} 