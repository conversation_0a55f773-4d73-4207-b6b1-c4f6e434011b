# 功能说明

承担页面中的信息提取、按钮嵌入功能。

# 基本过程

2.  **页面数据准备**: 适配器预加载所需数据，例如用于定位文献条目的 CSS 选择器。
3.  **UI 注入**:
    -   如果存在有效的选择器，适配器会用它来定位页面上所有的文献条目。
    -   为每个条目动态添加交互式UI组件（如“总结”、“下载”按钮）。
    -   如果选择器不存在或失效，则会触发一个后台 AI 任务来学习新的选择器。
4.  **元数据提取**:
    -   将定位到的文献条目 HTML 发送到后台服务，以异步方式提取详细的元数据（标题、作者、PDF链接等）。
    -   在 `GoogleScholarAdapter` 中，为了更精确地提取“All Versions”链接，代码会查找文本内容包含 "all" 和 "version"（英文）或“所有”和“版本”（中文）关键字的`<a>`标签。同时，它会直接查找并提取以 `.pdf` 结尾的链接作为PDF下载地址。
5.  **用户交互**:
    -   用户通过注入的 UI 与插件交互（例如，点击“总结”）。
    -   适配器捕获这些事件，并调用相应的服务（如 `aiService`）来完成任务。

