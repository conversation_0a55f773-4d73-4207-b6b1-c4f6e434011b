/**
 * PopupWindow.css
 * 
 * Styles for the popup window component
 */

/* 弹出窗口 */
.rs-popup {
  position: fixed;
  bottom: 100px;
  right: 30px;
  width: 520px;
  max-height: 70vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  display: none;
  flex-direction: column;
  animation: rs-popup-show 0.3s ease;
  overflow: hidden;
}

@keyframes rs-popup-show {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 弹出窗口头部 */
.rs-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #4285f4;
  color: white;
}

.rs-popup-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.rs-popup-close {
  background: none !important;
  border: none;
  color: white !important;
  font-size: 24px;
  cursor: pointer;
  padding: 0 !important;
  margin: 0 !important;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  border-radius: 50%;
}

.rs-popup-close:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* 内容包装器 */
.rs-popup-content-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

/* 弹出窗口内容 */
.rs-popup-content {
  padding: 16px;
  padding-bottom: 0; /* Remove bottom padding to avoid double padding with fixed action buttons */
  overflow-y: auto;
  flex: 1;
}

.rs-popup-query {
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 15px;
}

.rs-popup-paper-count {
  color: #666;
  margin-bottom: 16px;
  font-size: 14px;
}

/* 批处理按钮 */
.rs-popup-batch-actions {
  display: flex;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

/* 论文列表容器 */
.rs-popup-paper-list-container {
  overflow-y: auto;
  max-height: calc(50vh - 120px); /* Adjust for header and other fixed elements */
  margin-bottom: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px;
}

/* 论文列表 */
.rs-popup-paper-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rs-popup-paper-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.rs-popup-paper-info {
  flex: 1;
}

.rs-popup-paper-title {
  font-weight: 500;
  margin-bottom: 4px;
  font-size: 15px;
}

.rs-popup-paper-authors,
.rs-popup-paper-year {
  color: #666;
  font-size: 13px;
}

.rs-popup-paper-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Toggle switch styles */
.rs-toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.rs-toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.rs-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.rs-toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .rs-toggle-slider {
  background-color: #2196F3;
}

input:checked + .rs-toggle-slider:before {
  transform: translateX(26px);
}

/* 操作按钮固定在底部 */
.rs-action-buttons-fixed {
  position: sticky;
  bottom: 0;
  background-color: white;
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  z-index: 1;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
}

/* Paper remove button (SVG icon) */
.rs-popup-paper-remove {
  color: #999;
  font-size: 18px;
  cursor: pointer;
  padding: 0 8px;
  align-self: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rs-popup-paper-remove:hover {
  color: #f44336;
}

.rs-popup-paper-remove svg {
  width: 18px;
  height: 18px;
  stroke: currentColor;
}

/* Delete icon image */
.rs-delete-icon {
  width: 18px;
  height: 18px;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.rs-popup-paper-remove:hover .rs-delete-icon {
  opacity: 1;
}

/* Action buttons section */
.rs-action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Toggle options */
.rs-toggle-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

/* Start organize button */
.rs-start-organize-btn {
  margin-top: 10px;
  padding: 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.3s ease;
}

.rs-start-organize-btn:hover {
  background-color: #45a049;
}

/* Loading and success states */
.rs-loading {
  position: relative;
  color: transparent !important;
}

.rs-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  margin-left: -8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.rs-success {
  background-color: #34a853 !important;
} 