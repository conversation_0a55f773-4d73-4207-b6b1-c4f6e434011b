/**
 * SummaryContainer.css
 * 
 * Styles for the summary container that appears when a paper is summarized
 */

.rs-summary-container {
  margin-top: 12px;
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: #f1f8ff;
  border: 1px solid #c0d7ea;
  position: relative;
}

.rs-summary-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #4285f4;
}

.rs-summary-content {
  font-size: 14px;
  line-height: 1.5;
}

.rs-summary-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.rs-summary-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.rs-summary-error {
  background-color: #fef2f2;
  border-color: #f87171;
}

.rs-error-message {
  color: #b91c1c;
} 