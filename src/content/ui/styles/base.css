/**
 * base.css
 * 
 * Common styles shared across components
 */

/* 基本样式重置 */
.rs-controls, 
.rs-floating-button,
.rs-popup,
.rs-summary-container {
  all: revert;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 通用按钮样式 */
.rs-controls button,
.rs-popup button {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-right: 8px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s, transform 0.1s;
}

.rs-controls button:hover,
.rs-popup button:hover {
  background-color: #1a73e8;
}

.rs-controls button:active,
.rs-popup button:active {
  transform: scale(0.97);
}

.rs-controls button:disabled,
.rs-popup button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 工具提示 */
.rs-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
}

/* 加载中状态 */
.rs-loading {
  position: relative;
  opacity: 0.7;
}

/* 成功状态 */
.rs-success {
  background-color: #34a853 !important;
}

/* 响应式样式 */
@media (max-width: 600px) {
  .rs-popup {
    width: 90%;
    right: 5%;
    left: 5%;
  }
  
  .rs-popup-batch-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .rs-popup-paper-item {
    flex-direction: column;
  }
  
  .rs-popup-paper-actions {
    flex-direction: row;
  }
} 