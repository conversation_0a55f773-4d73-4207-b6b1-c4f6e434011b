{"name": "research-paper-summarizer", "version": "1.0.0", "description": "A Chrome extension that summarizes research papers from search results.", "scripts": {"build": "webpack --config webpack.config.js", "dev": "webpack --watch --config webpack.config.js", "lint": "eslint src/**/*.js", "test": "jest"}, "dependencies": {"@babel/runtime": "^7.27.6", "axios": "^1.4.0", "pdfjs-dist": "^3.8.162"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/plugin-transform-runtime": "^7.28.0", "@babel/preset-env": "^7.22.5", "babel-loader": "^9.1.2", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "eslint": "^8.43.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.5.0", "style-loader": "^3.3.3", "webpack": "^5.99.8", "webpack-cli": "^5.1.4"}, "main": "webpack.config.js", "repository": {"type": "git", "url": "git+https://github.com/ovenKiller/LitHelper.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/ovenKiller/LitHelper/issues"}, "homepage": "https://github.com/ovenKiller/LitHelper#readme"}