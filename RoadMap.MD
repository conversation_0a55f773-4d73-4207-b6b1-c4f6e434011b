- [✅] 增加设置页面
- [✅]重新优化代码架构
- [✅] 优化background.js代码的架构，按照功能分层
- [✅] 优化论文的提取逻辑，论文的页面内容提取放在content目录下，深层内容提取放在background下，想办法协调整个的回调逻辑。在加入到论文盒子后开始深层内容提取。
- [ ] 将google scholar提取论文项的逻辑设置为AI自动提取。







# 功能点构思

- 首先，做好信息提取功能。
在页面加载后，提取出不需要二次浏览的所有信息。

- 加入到论文盒子内后，再进行解析。解析得到的是论文所有的所需信息。
- 根据解析的信息，做进一步的操作，例如下载、总结。


## 论文解析过程
### 解析目标
- 每个论文的基本信息。
- 所有的引用版本（引用可能有多种）

### 解析过程的实现
- 定义两个动作：fetch和使用xpath过滤。
- 建立一个解析动作字典：key是网站的url，value就是序列。
- 建立一个网站库字典：key是网站的url，value是网页的url。这个字典用于优化路径。
- 建立一个仅大模型可解析的网站名单。这里可以解析出内容，但是路径经常变，导致只能使用大模型。
- 定义一个不可解析名单：这里的网站可能根本提取不出信息，或者比较麻烦，因此只会以较低的概率被访问到、或者不访问。
一个论文通常有多个version、这些version还可能分版本。
先找到其中有效的version（包含动作词典的网站）
根据动作解析出内容。
如果解析失败，则使用大模型分别找到新url和旧url的解析路径。最后取交集。如果找不到解析路径，将其放在不可解析名单里，如果取不到交集，则把网站放在仅大模型可解析的网站名单里。

然后寻找不在不可解析名单、不在仅大模型可解析名单的网址。
解析成功的话，保存url以及解析路径。

最后以一定概率访问剩余的版本，访问概率可以控制。


